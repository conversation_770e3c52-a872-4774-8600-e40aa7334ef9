/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/cluster-key-slot";
exports.ids = ["vendor-chunks/cluster-key-slot"];
exports.modules = {

/***/ "(rsc)/./node_modules/cluster-key-slot/lib/index.js":
/*!****************************************************!*\
  !*** ./node_modules/cluster-key-slot/lib/index.js ***!
  \****************************************************/
/***/ ((module) => {

eval("/*\n * Copyright 2001-2010 Georges Menie (www.menie.org)\n * Copyright 2010 Salvatore Sanfilippo (adapted to Redis coding style)\n * Copyright 2015 Zihua Li (http://zihua.li) (ported to JavaScript)\n * Copyright 2016 Mike Diarmid (http://github.com/salakar) (re-write for performance, ~700% perf inc)\n * All rights reserved.\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions are met:\n *\n *     * Redistributions of source code must retain the above copyright\n *       notice, this list of conditions and the following disclaimer.\n *     * Redistributions in binary form must reproduce the above copyright\n *       notice, this list of conditions and the following disclaimer in the\n *       documentation and/or other materials provided with the distribution.\n *     * Neither the name of the University of California, Berkeley nor the\n *       names of its contributors may be used to endorse or promote products\n *       derived from this software without specific prior written permission.\n *\n * THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND ANY\n * EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE\n * DISCLAIMED. IN NO EVENT SHALL THE REGENTS AND CONTRIBUTORS BE LIABLE FOR ANY\n * DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES\n * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;\n * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND\n * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS\n * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n */\n\n/* CRC16 implementation according to CCITT standards.\n *\n * Note by @antirez: this is actually the XMODEM CRC 16 algorithm, using the\n * following parameters:\n *\n * Name                       : \"XMODEM\", also known as \"ZMODEM\", \"CRC-16/ACORN\"\n * Width                      : 16 bit\n * Poly                       : 1021 (That is actually x^16 + x^12 + x^5 + 1)\n * Initialization             : 0000\n * Reflect Input byte         : False\n * Reflect Output CRC         : False\n * Xor constant to output CRC : 0000\n * Output for \"123456789\"     : 31C3\n */\n\nvar lookup = [\n  0x0000, 0x1021, 0x2042, 0x3063, 0x4084, 0x50a5, 0x60c6, 0x70e7,\n  0x8108, 0x9129, 0xa14a, 0xb16b, 0xc18c, 0xd1ad, 0xe1ce, 0xf1ef,\n  0x1231, 0x0210, 0x3273, 0x2252, 0x52b5, 0x4294, 0x72f7, 0x62d6,\n  0x9339, 0x8318, 0xb37b, 0xa35a, 0xd3bd, 0xc39c, 0xf3ff, 0xe3de,\n  0x2462, 0x3443, 0x0420, 0x1401, 0x64e6, 0x74c7, 0x44a4, 0x5485,\n  0xa56a, 0xb54b, 0x8528, 0x9509, 0xe5ee, 0xf5cf, 0xc5ac, 0xd58d,\n  0x3653, 0x2672, 0x1611, 0x0630, 0x76d7, 0x66f6, 0x5695, 0x46b4,\n  0xb75b, 0xa77a, 0x9719, 0x8738, 0xf7df, 0xe7fe, 0xd79d, 0xc7bc,\n  0x48c4, 0x58e5, 0x6886, 0x78a7, 0x0840, 0x1861, 0x2802, 0x3823,\n  0xc9cc, 0xd9ed, 0xe98e, 0xf9af, 0x8948, 0x9969, 0xa90a, 0xb92b,\n  0x5af5, 0x4ad4, 0x7ab7, 0x6a96, 0x1a71, 0x0a50, 0x3a33, 0x2a12,\n  0xdbfd, 0xcbdc, 0xfbbf, 0xeb9e, 0x9b79, 0x8b58, 0xbb3b, 0xab1a,\n  0x6ca6, 0x7c87, 0x4ce4, 0x5cc5, 0x2c22, 0x3c03, 0x0c60, 0x1c41,\n  0xedae, 0xfd8f, 0xcdec, 0xddcd, 0xad2a, 0xbd0b, 0x8d68, 0x9d49,\n  0x7e97, 0x6eb6, 0x5ed5, 0x4ef4, 0x3e13, 0x2e32, 0x1e51, 0x0e70,\n  0xff9f, 0xefbe, 0xdfdd, 0xcffc, 0xbf1b, 0xaf3a, 0x9f59, 0x8f78,\n  0x9188, 0x81a9, 0xb1ca, 0xa1eb, 0xd10c, 0xc12d, 0xf14e, 0xe16f,\n  0x1080, 0x00a1, 0x30c2, 0x20e3, 0x5004, 0x4025, 0x7046, 0x6067,\n  0x83b9, 0x9398, 0xa3fb, 0xb3da, 0xc33d, 0xd31c, 0xe37f, 0xf35e,\n  0x02b1, 0x1290, 0x22f3, 0x32d2, 0x4235, 0x5214, 0x6277, 0x7256,\n  0xb5ea, 0xa5cb, 0x95a8, 0x8589, 0xf56e, 0xe54f, 0xd52c, 0xc50d,\n  0x34e2, 0x24c3, 0x14a0, 0x0481, 0x7466, 0x6447, 0x5424, 0x4405,\n  0xa7db, 0xb7fa, 0x8799, 0x97b8, 0xe75f, 0xf77e, 0xc71d, 0xd73c,\n  0x26d3, 0x36f2, 0x0691, 0x16b0, 0x6657, 0x7676, 0x4615, 0x5634,\n  0xd94c, 0xc96d, 0xf90e, 0xe92f, 0x99c8, 0x89e9, 0xb98a, 0xa9ab,\n  0x5844, 0x4865, 0x7806, 0x6827, 0x18c0, 0x08e1, 0x3882, 0x28a3,\n  0xcb7d, 0xdb5c, 0xeb3f, 0xfb1e, 0x8bf9, 0x9bd8, 0xabbb, 0xbb9a,\n  0x4a75, 0x5a54, 0x6a37, 0x7a16, 0x0af1, 0x1ad0, 0x2ab3, 0x3a92,\n  0xfd2e, 0xed0f, 0xdd6c, 0xcd4d, 0xbdaa, 0xad8b, 0x9de8, 0x8dc9,\n  0x7c26, 0x6c07, 0x5c64, 0x4c45, 0x3ca2, 0x2c83, 0x1ce0, 0x0cc1,\n  0xef1f, 0xff3e, 0xcf5d, 0xdf7c, 0xaf9b, 0xbfba, 0x8fd9, 0x9ff8,\n  0x6e17, 0x7e36, 0x4e55, 0x5e74, 0x2e93, 0x3eb2, 0x0ed1, 0x1ef0\n];\n\n/**\n * Convert a string to a UTF8 array - faster than via buffer\n * @param str\n * @returns {Array}\n */\nvar toUTF8Array = function toUTF8Array(str) {\n  var char;\n  var i = 0;\n  var p = 0;\n  var utf8 = [];\n  var len = str.length;\n\n  for (; i < len; i++) {\n    char = str.charCodeAt(i);\n    if (char < 128) {\n      utf8[p++] = char;\n    } else if (char < 2048) {\n      utf8[p++] = (char >> 6) | 192;\n      utf8[p++] = (char & 63) | 128;\n    } else if (\n        ((char & 0xFC00) === 0xD800) && (i + 1) < str.length &&\n        ((str.charCodeAt(i + 1) & 0xFC00) === 0xDC00)) {\n      char = 0x10000 + ((char & 0x03FF) << 10) + (str.charCodeAt(++i) & 0x03FF);\n      utf8[p++] = (char >> 18) | 240;\n      utf8[p++] = ((char >> 12) & 63) | 128;\n      utf8[p++] = ((char >> 6) & 63) | 128;\n      utf8[p++] = (char & 63) | 128;\n    } else {\n      utf8[p++] = (char >> 12) | 224;\n      utf8[p++] = ((char >> 6) & 63) | 128;\n      utf8[p++] = (char & 63) | 128;\n    }\n  }\n\n  return utf8;\n};\n\n/**\n * Convert a string into a redis slot hash.\n * @param str\n * @returns {number}\n */\nvar generate = module.exports = function generate(str) {\n  var char;\n  var i = 0;\n  var start = -1;\n  var result = 0;\n  var resultHash = 0;\n  var utf8 = typeof str === 'string' ? toUTF8Array(str) : str;\n  var len = utf8.length;\n\n  while (i < len) {\n    char = utf8[i++];\n    if (start === -1) {\n      if (char === 0x7B) {\n        start = i;\n      }\n    } else if (char !== 0x7D) {\n      resultHash = lookup[(char ^ (resultHash >> 8)) & 0xFF] ^ (resultHash << 8);\n    } else if (i - 1 !== start) {\n      return resultHash & 0x3FFF;\n    }\n\n    result = lookup[(char ^ (result >> 8)) & 0xFF] ^ (result << 8);\n  }\n\n  return result & 0x3FFF;\n};\n\n/**\n * Convert an array of multiple strings into a redis slot hash.\n * Returns -1 if one of the keys is not for the same slot as the others\n * @param keys\n * @returns {number}\n */\nmodule.exports.generateMulti = function generateMulti(keys) {\n  var i = 1;\n  var len = keys.length;\n  var base = generate(keys[0]);\n\n  while (i < len) {\n    if (generate(keys[i++]) !== base) return -1;\n  }\n\n  return base;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/cluster-key-slot/lib/index.js\n");

/***/ })

};
;