"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(app-pages-browser)/./src/contexts/LanguageContext.tsx\");\n/* harmony import */ var _contexts_DropdownContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/DropdownContext */ \"(app-pages-browser)/./src/contexts/DropdownContext.tsx\");\n/* harmony import */ var _LanguageSwitcher__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./LanguageSwitcher */ \"(app-pages-browser)/./src/components/LanguageSwitcher.tsx\");\n/* harmony import */ var _UserMenu__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./UserMenu */ \"(app-pages-browser)/./src/components/UserMenu.tsx\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction Header(param) {\n    let { onAuthClick, onHistoryClick, showBackButton = false, onBackClick, showScore = false, score, onResetScore } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { t } = (0,_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_3__.useLanguage)();\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Header.useEffect\": ()=>{\n            const handleScroll = {\n                \"Header.useEffect.handleScroll\": ()=>{\n                    const scrollTop = window.scrollY;\n                    setIsScrolled(scrollTop > 20);\n                }\n            }[\"Header.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"Header.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"Header.useEffect\"];\n        }\n    }[\"Header.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_DropdownContext__WEBPACK_IMPORTED_MODULE_4__.DropdownProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n            className: \"fixed top-0 left-0 right-0 z-50 transition-all duration-500 \".concat(isScrolled ? 'glass backdrop-blur-md shadow-lg shadow-black/5' : 'bg-transparent'),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-16 gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 sm:space-x-4 flex-shrink-0\",\n                            children: [\n                                showBackButton && onBackClick && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onBackClick,\n                                    className: \"group flex items-center space-x-2 px-4 py-2.5 rounded-xl transition-all duration-500 shadow-sm hover:shadow-md \".concat(isScrolled ? 'bg-white/80 hover:bg-white/90 text-gray-700 hover:text-gray-900' : 'bg-white/80 hover:bg-white/90 text-gray-700 hover:text-gray-900 backdrop-blur-sm'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4 transition-transform group-hover:-translate-x-0.5\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M15 19l-7-7 7-7\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-semibold\",\n                                            children: t('backToLevels')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 min-w-0 flex-1 sm:flex-initial\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 relative flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                src: \"/logo-default.png\",\n                                                alt: \"Infinite English - Học Tiếng Anh với AI\",\n                                                className: \"w-full h-full object-contain\",\n                                                width: 100,\n                                                height: 100\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden sm:block min-w-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold transition-all duration-500 \".concat(isScrolled ? 'text-secondary-30' : 'text-secondary-dark drop-shadow-lg'),\n                                                children: \"Infinite English\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"sm:hidden min-w-0 flex-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-lg font-bold transition-all duration-500 truncate \".concat(isScrolled ? 'text-secondary-30' : 'text-secondary-dark drop-shadow-lg'),\n                                                children: \"Infinite English\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"hidden md:flex items-center space-x-1 ml-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/\",\n                                            className: \"px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 \".concat(isScrolled ? 'text-gray-700 hover:text-secondary-30 hover:bg-secondary-30/10' : ' text-gray-300 hover:text-secondary-30 hover:bg-white/50'),\n                                            children: \"Quiz\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/lessons\",\n                                            className: \"px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 \".concat(isScrolled ? 'text-gray-700 hover:text-secondary-30 hover:bg-secondary-30/10' : 'text-gray-300 hover:text-secondary-30 hover:bg-white/50'),\n                                            children: \"AI Lessons\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/reading\",\n                                            className: \"px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 \".concat(isScrolled ? 'text-gray-700 hover:text-secondary-30 hover:bg-secondary-30/10' : 'text-gray-300 hover:text-secondary-30 hover:bg-white/50'),\n                                            children: \"Reading\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/tutor\",\n                                            className: \"px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 \".concat(isScrolled ? 'text-gray-700 hover:text-secondary-30 hover:bg-secondary-30/10' : 'text-gray-300 hover:text-secondary-30 hover:bg-white/50'),\n                                            children: \"AI Tutor\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this),\n                        showScore && score && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden sm:flex items-center space-x-3 flex-shrink-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 px-3 py-2 rounded-xl shadow-sm transition-all duration-500 \".concat(isScrolled ? 'bg-gradient-to-r from-secondary-30/10 to-highlight/10 text-secondary-dark' : 'bg-gradient-to-r from-secondary-30/10 to-highlight/10 text-secondary-dark backdrop-blur-sm'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-6 h-6 rounded-full flex items-center justify-center transition-all duration-500 \".concat(isScrolled ? 'bg-secondary-30/20' : 'bg-secondary-30/20'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-3 h-3 transition-colors duration-500 \".concat(isScrolled ? 'text-emerald-600' : 'text-emerald-600'),\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-bold\",\n                                                    children: [\n                                                        t('score'),\n                                                        \": \",\n                                                        score.correct,\n                                                        \"/\",\n                                                        score.total\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs transition-colors duration-500 \".concat(isScrolled ? 'text-emerald-600' : 'text-emerald-600'),\n                                                    children: [\n                                                        score.total > 0 ? Math.round(score.correct / score.total * 100) : 0,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 15\n                                }, this),\n                                onResetScore && score.total > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onResetScore,\n                                    className: \"group p-2 rounded-lg transition-all duration-500 shadow-sm hover:shadow-md \".concat(isScrolled ? 'bg-red-50 hover:bg-red-100 text-red-600 hover:text-red-700' : 'bg-red-50 hover:bg-red-100 text-red-600 hover:text-red-700'),\n                                    title: t('resetScore') || 'Reset Score',\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 13\n                        }, this),\n                        showScore && score && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"sm:hidden flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1 px-2 py-1 rounded-lg text-xs font-bold transition-all duration-500 \".concat(isScrolled ? 'bg-gradient-to-r from-emerald-50 to-blue-50 text-emerald-700' : 'bg-gradient-to-r from-emerald-50 to-blue-50 text-emerald-700 backdrop-blur-sm'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        score.correct,\n                                        \"/\",\n                                        score.total\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 sm:space-x-4 flex-shrink-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LanguageSwitcher__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    isScrolled: isScrolled\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 13\n                                }, this),\n                                user ? onHistoryClick && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UserMenu__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    onHistoryClick: onHistoryClick\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 33\n                                }, this) : onAuthClick && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onAuthClick,\n                                    className: \"group relative px-6 py-2.5 rounded-xl transition-all duration-500 text-sm font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 \".concat(isScrolled ? 'gradient-secondary hover:bg-secondary-dark text-white' : 'gradient-secondary hover:bg-secondary-dark text-white backdrop-blur-sm'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"relative z-10\",\n                                            children: t('login')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 19\n                                        }, this),\n                                        isScrolled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-xl\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n            lineNumber: 46,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n_s(Header, \"RqXGv3cvLAemF5k+prPSIEqRBbQ=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_3__.useLanguage\n    ];\n});\n_c = Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Header.tsx\n"));

/***/ })

});