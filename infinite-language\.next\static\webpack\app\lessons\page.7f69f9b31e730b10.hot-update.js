"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/lessons/page",{

/***/ "(app-pages-browser)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(app-pages-browser)/./src/contexts/LanguageContext.tsx\");\n/* harmony import */ var _contexts_DropdownContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/DropdownContext */ \"(app-pages-browser)/./src/contexts/DropdownContext.tsx\");\n/* harmony import */ var _LanguageSwitcher__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./LanguageSwitcher */ \"(app-pages-browser)/./src/components/LanguageSwitcher.tsx\");\n/* harmony import */ var _UserMenu__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./UserMenu */ \"(app-pages-browser)/./src/components/UserMenu.tsx\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction Header(param) {\n    let { onAuthClick, onHistoryClick, showBackButton = false, onBackClick, showScore = false, score, onResetScore } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { t } = (0,_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_3__.useLanguage)();\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Header.useEffect\": ()=>{\n            const handleScroll = {\n                \"Header.useEffect.handleScroll\": ()=>{\n                    const scrollTop = window.scrollY;\n                    setIsScrolled(scrollTop > 20);\n                }\n            }[\"Header.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"Header.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"Header.useEffect\"];\n        }\n    }[\"Header.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_DropdownContext__WEBPACK_IMPORTED_MODULE_4__.DropdownProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n            className: \"fixed top-0 left-0 right-0 z-50 transition-all duration-500 \".concat(isScrolled ? 'glass backdrop-blur-md shadow-lg shadow-black/5' : 'bg-transparent'),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-16 gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 sm:space-x-4 flex-shrink-0\",\n                            children: [\n                                showBackButton && onBackClick && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onBackClick,\n                                    className: \"group flex items-center space-x-2 px-4 py-2.5 rounded-xl transition-all duration-500 shadow-sm hover:shadow-md \".concat(isScrolled ? 'bg-white/80 hover:bg-white/90 text-gray-700 hover:text-gray-900' : 'bg-white/80 hover:bg-white/90 text-gray-700 hover:text-gray-900 backdrop-blur-sm'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4 transition-transform group-hover:-translate-x-0.5\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M15 19l-7-7 7-7\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-semibold\",\n                                            children: t('backToLevels')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 min-w-0 flex-1 sm:flex-initial\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 relative flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                src: \"/logo-default.png\",\n                                                alt: \"Infinite English - Học Tiếng Anh với AI\",\n                                                className: \"object-contain\",\n                                                width: 500,\n                                                height: 100\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden sm:block min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-xl font-bold transition-all duration-500 \".concat(isScrolled ? 'text-secondary-30' : 'text-secondary-dark drop-shadow-lg'),\n                                                    children: \"Infinite English\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs font-medium transition-all duration-500 max-w-xs truncate \".concat(isScrolled ? 'text-gray-500' : 'text-gray-600 drop-shadow-md'),\n                                                    children: t('appSubtitle')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"sm:hidden min-w-0 flex-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-lg font-bold transition-all duration-500 truncate \".concat(isScrolled ? 'text-secondary-30' : 'text-secondary-dark drop-shadow-lg'),\n                                                children: \"Infinite English\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"hidden md:flex items-center space-x-1 ml-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/\",\n                                            className: \"px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 \".concat(isScrolled ? 'text-gray-700 hover:text-secondary-30 hover:bg-secondary-30/10' : ' text-gray-300 hover:text-secondary-30 hover:bg-white/50'),\n                                            children: \"Quiz\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/lessons\",\n                                            className: \"px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 \".concat(isScrolled ? 'text-gray-700 hover:text-secondary-30 hover:bg-secondary-30/10' : 'text-gray-300 hover:text-secondary-30 hover:bg-white/50'),\n                                            children: \"AI Lessons\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/reading\",\n                                            className: \"px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 \".concat(isScrolled ? 'text-gray-700 hover:text-secondary-30 hover:bg-secondary-30/10' : 'text-gray-300 hover:text-secondary-30 hover:bg-white/50'),\n                                            children: \"Reading\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/tutor\",\n                                            className: \"px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 \".concat(isScrolled ? 'text-gray-700 hover:text-secondary-30 hover:bg-secondary-30/10' : 'text-gray-300 hover:text-secondary-30 hover:bg-white/50'),\n                                            children: \"AI Tutor\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this),\n                        showScore && score && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden sm:flex items-center space-x-3 flex-shrink-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 px-3 py-2 rounded-xl shadow-sm transition-all duration-500 \".concat(isScrolled ? 'bg-gradient-to-r from-secondary-30/10 to-highlight/10 text-secondary-dark' : 'bg-gradient-to-r from-secondary-30/10 to-highlight/10 text-secondary-dark backdrop-blur-sm'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-6 h-6 rounded-full flex items-center justify-center transition-all duration-500 \".concat(isScrolled ? 'bg-secondary-30/20' : 'bg-secondary-30/20'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-3 h-3 transition-colors duration-500 \".concat(isScrolled ? 'text-emerald-600' : 'text-emerald-600'),\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-bold\",\n                                                    children: [\n                                                        t('score'),\n                                                        \": \",\n                                                        score.correct,\n                                                        \"/\",\n                                                        score.total\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs transition-colors duration-500 \".concat(isScrolled ? 'text-emerald-600' : 'text-emerald-600'),\n                                                    children: [\n                                                        score.total > 0 ? Math.round(score.correct / score.total * 100) : 0,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 15\n                                }, this),\n                                onResetScore && score.total > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onResetScore,\n                                    className: \"group p-2 rounded-lg transition-all duration-500 shadow-sm hover:shadow-md \".concat(isScrolled ? 'bg-red-50 hover:bg-red-100 text-red-600 hover:text-red-700' : 'bg-red-50 hover:bg-red-100 text-red-600 hover:text-red-700'),\n                                    title: t('resetScore') || 'Reset Score',\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 13\n                        }, this),\n                        showScore && score && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"sm:hidden flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1 px-2 py-1 rounded-lg text-xs font-bold transition-all duration-500 \".concat(isScrolled ? 'bg-gradient-to-r from-emerald-50 to-blue-50 text-emerald-700' : 'bg-gradient-to-r from-emerald-50 to-blue-50 text-emerald-700 backdrop-blur-sm'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        score.correct,\n                                        \"/\",\n                                        score.total\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 sm:space-x-4 flex-shrink-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LanguageSwitcher__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    isScrolled: isScrolled\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 13\n                                }, this),\n                                user ? onHistoryClick && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UserMenu__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    onHistoryClick: onHistoryClick\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 33\n                                }, this) : onAuthClick && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onAuthClick,\n                                    className: \"group relative px-6 py-2.5 rounded-xl transition-all duration-500 text-sm font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 \".concat(isScrolled ? 'gradient-secondary hover:bg-secondary-dark text-white' : 'gradient-secondary hover:bg-secondary-dark text-white backdrop-blur-sm'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"relative z-10\",\n                                            children: t('login')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 19\n                                        }, this),\n                                        isScrolled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-xl\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n            lineNumber: 46,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n_s(Header, \"RqXGv3cvLAemF5k+prPSIEqRBbQ=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_3__.useLanguage\n    ];\n});\n_c = Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Header.tsx\n"));

/***/ })

});