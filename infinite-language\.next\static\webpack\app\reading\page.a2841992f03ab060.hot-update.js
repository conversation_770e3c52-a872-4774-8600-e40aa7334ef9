"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/reading/page",{

/***/ "(app-pages-browser)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(app-pages-browser)/./src/contexts/LanguageContext.tsx\");\n/* harmony import */ var _contexts_DropdownContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/DropdownContext */ \"(app-pages-browser)/./src/contexts/DropdownContext.tsx\");\n/* harmony import */ var _LanguageSwitcher__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./LanguageSwitcher */ \"(app-pages-browser)/./src/components/LanguageSwitcher.tsx\");\n/* harmony import */ var _UserMenu__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./UserMenu */ \"(app-pages-browser)/./src/components/UserMenu.tsx\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction Header(param) {\n    let { onAuthClick, onHistoryClick, showBackButton = false, onBackClick, showScore = false, score, onResetScore } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { t } = (0,_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_3__.useLanguage)();\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Header.useEffect\": ()=>{\n            const handleScroll = {\n                \"Header.useEffect.handleScroll\": ()=>{\n                    const scrollTop = window.scrollY;\n                    setIsScrolled(scrollTop > 20);\n                }\n            }[\"Header.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"Header.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"Header.useEffect\"];\n        }\n    }[\"Header.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_DropdownContext__WEBPACK_IMPORTED_MODULE_4__.DropdownProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n            className: \"fixed top-0 left-0 right-0 z-50 transition-all duration-500 \".concat(isScrolled ? 'bg-white shadow-lg shadow-black/5' : 'bg-transparent'),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-16 gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 sm:space-x-4 flex-shrink-0\",\n                            children: [\n                                showBackButton && onBackClick && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onBackClick,\n                                    className: \"group flex items-center space-x-2 px-4 py-2.5 rounded-xl transition-all duration-500 shadow-sm hover:shadow-md \".concat(isScrolled ? 'bg-white/80 hover:bg-white/90 text-gray-700 hover:text-gray-900' : 'bg-white/80 hover:bg-white/90 text-gray-700 hover:text-gray-900 backdrop-blur-sm'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4 transition-transform group-hover:-translate-x-0.5\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M15 19l-7-7 7-7\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-semibold\",\n                                            children: t('backToLevels')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-0 min-w-0 flex-1 sm:flex-initial\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 relative flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                src: isScrolled ? \"/logo-default.png\" : \"/logo-default-white.png\",\n                                                alt: \"Infinite English - Học Tiếng Anh với AI\",\n                                                className: \"w-full h-full object-contain mt-0.5\",\n                                                width: 100,\n                                                height: 100\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden sm:block min-w-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl font-bold transition-all duration-500 \".concat(isScrolled ? 'text-secondary-30' : 'text-secondary-dark drop-shadow-lg'),\n                                                children: \"Infinite English\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"sm:hidden min-w-0 flex-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-lg font-bold transition-all duration-500 truncate \".concat(isScrolled ? 'text-secondary-30' : 'text-secondary-dark drop-shadow-lg'),\n                                                children: \"Infinite English\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"hidden md:flex items-center space-x-1 ml-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/\",\n                                            className: \"px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 \".concat(isScrolled ? 'text-gray-700 hover:text-secondary-30 hover:bg-secondary-30/10' : ' text-gray-300 hover:text-secondary-30 hover:bg-white/50'),\n                                            children: \"Quiz\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/lessons\",\n                                            className: \"px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 \".concat(isScrolled ? 'text-gray-700 hover:text-secondary-30 hover:bg-secondary-30/10' : 'text-gray-300 hover:text-secondary-30 hover:bg-white/50'),\n                                            children: \"AI Lessons\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/reading\",\n                                            className: \"px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 \".concat(isScrolled ? 'text-gray-700 hover:text-secondary-30 hover:bg-secondary-30/10' : 'text-gray-300 hover:text-secondary-30 hover:bg-white/50'),\n                                            children: \"Reading\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/tutor\",\n                                            className: \"px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 \".concat(isScrolled ? 'text-gray-700 hover:text-secondary-30 hover:bg-secondary-30/10' : 'text-gray-300 hover:text-secondary-30 hover:bg-white/50'),\n                                            children: \"AI Tutor\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this),\n                        showScore && score && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden sm:flex items-center space-x-3 flex-shrink-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 px-3 py-2 rounded-xl shadow-sm transition-all duration-500 \".concat(isScrolled ? 'bg-gradient-to-r from-secondary-30/10 to-highlight/10 text-secondary-dark' : 'bg-gradient-to-r from-secondary-30/10 to-highlight/10 text-secondary-dark backdrop-blur-sm'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-6 h-6 rounded-full flex items-center justify-center transition-all duration-500 \".concat(isScrolled ? 'bg-secondary-30/20' : 'bg-secondary-30/20'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-3 h-3 transition-colors duration-500 \".concat(isScrolled ? 'text-emerald-600' : 'text-emerald-600'),\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-bold\",\n                                                    children: [\n                                                        t('score'),\n                                                        \": \",\n                                                        score.correct,\n                                                        \"/\",\n                                                        score.total\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs transition-colors duration-500 \".concat(isScrolled ? 'text-emerald-600' : 'text-emerald-600'),\n                                                    children: [\n                                                        score.total > 0 ? Math.round(score.correct / score.total * 100) : 0,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 15\n                                }, this),\n                                onResetScore && score.total > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onResetScore,\n                                    className: \"group p-2 rounded-lg transition-all duration-500 shadow-sm hover:shadow-md \".concat(isScrolled ? 'bg-red-50 hover:bg-red-100 text-red-600 hover:text-red-700' : 'bg-red-50 hover:bg-red-100 text-red-600 hover:text-red-700'),\n                                    title: t('resetScore') || 'Reset Score',\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 13\n                        }, this),\n                        showScore && score && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"sm:hidden flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1 px-2 py-1 rounded-lg text-xs font-bold transition-all duration-500 \".concat(isScrolled ? 'bg-gradient-to-r from-emerald-50 to-blue-50 text-emerald-700' : 'bg-gradient-to-r from-emerald-50 to-blue-50 text-emerald-700 backdrop-blur-sm'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        score.correct,\n                                        \"/\",\n                                        score.total\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 sm:space-x-4 flex-shrink-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LanguageSwitcher__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    isScrolled: isScrolled\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 13\n                                }, this),\n                                user ? onHistoryClick && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UserMenu__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    onHistoryClick: onHistoryClick\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 33\n                                }, this) : onAuthClick && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onAuthClick,\n                                    className: \"group relative px-6 py-2.5 rounded-xl transition-all duration-500 text-sm font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 \".concat(isScrolled ? 'gradient-secondary hover:bg-secondary-dark text-white' : 'gradient-secondary hover:bg-secondary-dark text-white backdrop-blur-sm'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"relative z-10\",\n                                            children: t('login')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 19\n                                        }, this),\n                                        isScrolled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-xl\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n            lineNumber: 46,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n_s(Header, \"RqXGv3cvLAemF5k+prPSIEqRBbQ=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_3__.useLanguage\n    ];\n});\n_c = Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Header.tsx\n"));

/***/ })

});